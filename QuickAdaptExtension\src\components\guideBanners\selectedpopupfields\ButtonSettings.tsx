import React, { useEffect, useMemo, useState } from "react";
import {
	Dialog,
	DialogContent,
	useMediaQ<PERSON>y,
	useTheme,
	Box,
	IconButton,
	Popover,
	Typography,
	Button,
	FormControl,
	Select,
	MenuItem,
	TextField,
	SelectChangeEvent,
	RadioGroup,
	Radio,
	FormControlLabel,
	Input,
	ToggleButton,
	ToggleButtonGroup,
	Autocomplete,
	CircularProgress,
	DialogTitle,
	Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {
	AlignHorizontalLeft as TopLeftIcon,
	AlignHorizontalCenter as TopCenterIcon,
	AlignHorizontalRight as TopRightIcon,
	AlignVerticalTop as MiddleLeftIcon,
	AlignVerticalCenter as MiddleCenterIcon,
	AlignVerticalBottom as MiddleRightIcon,
	AlignHorizontalLeft as BottomLeftIcon,
	AlignHorizontalCenter as BottomCenterIcon,
	AlignHorizontalRight as BottomRightIcon,
} from "@mui/icons-material";
import "../../guideDesign/Canvas.module.scss";
import useDrawerStore, { TButtonAction, TInteractionValue } from "../../../store/drawerStore";
// import Draggable from "react-draggable";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import zIndex from "@mui/material/styles/zIndex";
import { useTranslation } from "react-i18next";

const ButtonSettings = () => {
	const { t: translate } = useTranslation();
	const {
		buttonsContainer,
		cloneButtonContainer,
		updateButton,
		addNewButton,
		deleteButton,
		deleteButtonContainer,
		updateContainer,
		setSettingAnchorEl,
		selectedTemplate,
		setSelectedTemplate,
		buttonProperty,
		setButtonProperty,
		setSelectActions,
		currentButtonName,
		setCurrentButtonName,
		targetURL,
		setTargetURL,
		selectedInteraction,
		setSelectedInteraction,
		openInteractionList,
		setOpenInteractionList,
		selectedTab,
		setSelectedTab,
		guideListByOrg,
		getGuildeListByOrg,
		loading,
		setLoading,
		updateButtonInteraction,
		updateButtonAction,
		settingAnchorEl,
		btnBgColor,
		btnTextColor,
		btnBorderColor,
		setBtnBgColor,
		setBtnTextColor,
		setBtnBorderColor,
		getCurrentButtonInfo,
		cuntainerId,
		setCuntainerId,
		buttonId,
		setButtonId,
		btnname,
		setBtnName,
		setIsUnSavedChanges,
		setIsThemeChanges,
		createWithAI,
		selectedTheme,
		applyThemeToAllButtons
	} = useDrawerStore((state) => state);
	const [selectedActions, setSelectedActions] = useState<TButtonAction>({
		value: "close", // Default action
		targetURL: "", // Default empty target URL
		tab: "same-tab", // Default tab (same-tab)
	});
	const [borderColor, setBorderColor] = useState("#000000");
	const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
	const [isOpen, setIsOpen] = useState(true);
	const [selectedPosition, setSelectedPosition] = useState("");
	const [url, setUrl] = useState("");
	const [action, setAction] = useState("close");
	const [openInNewTab, setOpenInNewTab] = useState(true);
	const userInfo = localStorage.getItem("userInfo");
	const userInfoObj = JSON.parse(userInfo || "{}");
	const orgDetails = JSON.parse(userInfoObj.orgDetails || "{}");
	const organizationId = orgDetails.OrganizationId;
	const defaultButtonColors = {
		backgroundColor: "#5F9EA0",
		borderColor: "#70afaf",
		color: "#ffffff",
	};
	const [tempColors, setTempColors] = useState(defaultButtonColors);
	const [colors, setColors] = useState({
		fill: "#4CAF50",
		border: "#4CAF50",
		text: "#ffffff",
	});

	// State variables for validation errors
	const [buttonNameError, setButtonNameError] = useState("");
	const [targetURLError, setTargetURLError] = useState("");

	useEffect(() => {
		const handleSelectButton = (containerId: any, buttonId: any) => {
			const selectedButton = getCurrentButtonInfo(containerId, buttonId);
			if (selectedButton) {
				setCurrentButtonName(selectedButton.title); // Save the initial button name
				setTargetURL(selectedButton.targetURL || "");
				setTempColors({
					backgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,
					borderColor: selectedButton.borderColor || defaultButtonColors.borderColor,
					color: selectedButton.textColor || defaultButtonColors.color,
				});
				setSelectedActions({
					value: selectedButton.selectedActions as TInteractionValue, // Use the actual saved action value
					targetURL: selectedButton.targetURL || "", // Use the saved target URL
					tab: selectedButton.tab || "same-tab", // Use the saved tab value
				});
				setSelectedTab(selectedButton.tab || "same-tab"); // Also set the selectedTab state
			}
		};
		handleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);
	}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);

	useEffect(() => {
		const buttonStyles = selectedTheme?.ThemeStyles?.Button;
		if (buttonStyles && settingAnchorEl.containerId && settingAnchorEl.buttonId) {
			setTempColors({
				backgroundColor: buttonStyles.primaryBtnBg || defaultButtonColors.backgroundColor,
				borderColor: buttonStyles.secondaryBtnBg || defaultButtonColors.borderColor,
				color: buttonStyles.primaryBtnColor || defaultButtonColors.color,
			});
			setButtonShape(buttonStyles.shape || "square");
	
			// Automatically apply the new button styles when theme changes
	applyThemeToAllButtons(selectedTheme?.ThemeStyles?.Button);
		}
	}, [selectedTheme]);
	
	  const [buttonShape, setButtonShape] = useState<"square" | "curved" | "round">("square");

// useEffect(() => {
//   const buttonStyles = selectedTheme?.ThemeStyles?.Button;
//   if (buttonStyles) {
//     setButtonShape(buttonStyles.shape || "square");
//   }
// }, [selectedTheme]);

	  
	const positions = [
		{ label: translate("Top Left", { defaultValue: "Top Left" }), icon: <TopLeftIcon fontSize="small" />, value: "top-left" },
		{ label: translate("Top Center", { defaultValue: "Top Center" }), icon: <TopCenterIcon fontSize="small" />, value: "top-center" },
		{ label: translate("Top Right", { defaultValue: "Top Right" }), icon: <TopRightIcon fontSize="small" />, value: "top-right" },
		{ label: translate("Middle Left", { defaultValue: "Middle Left" }), icon: <MiddleLeftIcon fontSize="small" />, value: "middle-left" },
		{ label: translate("Middle Center", { defaultValue: "Middle Center" }), icon: <MiddleCenterIcon fontSize="small" />, value: "middle-center" },
		{ label: translate("Middle Right", { defaultValue: "Middle Right" }), icon: <MiddleRightIcon fontSize="small" />, value: "middle-right" },
		{ label: translate("Bottom Left", { defaultValue: "Bottom Left" }), icon: <BottomLeftIcon fontSize="small" />, value: "bottom-left" },
		{ label: translate("Bottom Center", { defaultValue: "Bottom Center" }), icon: <BottomCenterIcon fontSize="small" />, value: "bottom-center" },
		{ label: translate("Bottom Right", { defaultValue: "Bottom Right" }), icon: <BottomRightIcon fontSize="small" />, value: "bottom-right" },
	];

	const curronButtonInfo = useMemo(() => {
		const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);
		setCurrentButtonName(result.title);
		return result;
	}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);

	const handlePositionClick = (position: any) => {
		setSelectedPosition(position);
	};

	const handleClose = () => {
		setButtonProperty(false);
	};

	if (!isOpen) return null;

	// const handleColorChange = (type: any, color: any) => {
	// 	setColors((prevColors) => ({ ...prevColors, [type]: color }));
	// };


	const handleColorChange = (e: any, targetName: any) => {
		const value = e.target.value;
		setTempColors((prev) => ({
			...prev,
			[targetName]: value,
		}));
	};

	const handleChangeActions = (e: SelectChangeEvent) => {
		const value: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue
		setSelectedActions({
			value: value, // Ensure that selectedActions.value is of type TInteractionValue
			targetURL: targetURL,
			tab: selectedTab as "same-tab" | "new-tab", // Ensure tab is a valid value
		});
	};
	const handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {
		setSelectedTab((event.target as HTMLInputElement).value);
	};
	const handleCloseInteraction = () => {
		setOpenInteractionList(false);
	};

	const handleOpenInteraction = () => {
		setOpenInteractionList(true);
		if (organizationId && !guideListByOrg.length) {
			(async () => {
				setLoading(true);
				await getGuildeListByOrg(organizationId);
				setLoading(false);
			})();
		}
	};

	const validateTargetURL = (url: string) => {
		if (selectedActions.value === "open-url") {
			if (!url) {
				return "URL is required";
			}
			try {
				new URL(url);
				return "";
			} catch (error) {
				return "Invalid URL";
			}
		}
		return "";
	};

	const handleApplyChanges = (containerId: any, buttonId: any) => {
		setCuntainerId(containerId);
		setButtonId(buttonId);

		const targetURLError = validateTargetURL(targetURL);
		setTargetURLError(targetURLError);

		if (targetURLError) {
			return;
		}

		// Retain the previously saved button name if the field is empty
		const buttonNameToUpdate = !currentButtonName || !currentButtonName.trim() ? getCurrentButtonInfo(containerId, buttonId).title : currentButtonName;
		setCurrentButtonName(buttonNameToUpdate);

		// Update button properties
		updateButton(containerId, buttonId, "style", tempColors);
		updateButton(containerId, buttonId, "name", buttonNameToUpdate);

		// Update button actions with the complete action object
		const actionToSave = {
			value: selectedActions.value,
			targetURL: targetURL,
			tab: selectedTab as "same-tab" | "new-tab",
			interaction: selectedInteraction,
		};
		updateButton(containerId, buttonId, "actions", actionToSave);
		updateButtonAction(containerId, buttonId, actionToSave);
		setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
		handleClose();
		setIsUnSavedChanges(true);
		setIsThemeChanges(true);
		// Clear selection
		//setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
	};
	const handleURLChange = (e: any) => {
		const newURL = e.target.value;
		setTargetURL(newURL);
		setSelectedActions({
			value: selectedActions.value,
			targetURL: newURL,
			tab: selectedTab as "same-tab" | "new-tab",
		});
	}

	return (
		//<Draggable>
		<div
			className="qadpt-designpopup qadpt-banbtnprop"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<div className="qadpt-title">{translate("Properties", { defaultValue: "Properties" })}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={() => handleClose()}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div className="qadpt-canblock qadpt-btnpro">
				<div className="qadpt-controls">
				<FormControl
					fullWidth
					sx={{ marginBottom: "5px" }}
				>
					<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px", textAlign: "left" }}>
								{translate("Button Name", { defaultValue: "Button Name" })}
					</Typography>
					<TextField
						value={currentButtonName}
						size="small"
								sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
								placeholder={translate("Button Name", { defaultValue: "Button Name" })}
						onChange={(e) => {
							setCurrentButtonName(e.target.value);
							// setBtnName(e.target.value);
						}}
					/>
					<Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px", textAlign: "left" }}>
								{translate("Button Action", { defaultValue: "Button Action" })}
					</Typography>
					<Select
							value={selectedActions.value}
							onChange={handleChangeActions}
						
								sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
									textAlign: "left",
									"& .MuiSelect-select": {
								padding: "8px",
							},
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
						MenuProps={{
							PaperProps: {},
						}}
					>
						<MenuItem value="close">{translate("Close", { defaultValue: "Close" })}</MenuItem>
						<MenuItem value="open-url">{translate("Open URL", { defaultValue: "Open URL" })}</MenuItem>
						{(selectedTemplate === "Tour" ||(selectedTemplate !== "Banner" && selectedTemplate !== "Hotspot")
							? [
								<MenuItem key="next" value="Next">{translate("Next", { defaultValue: "Next" })}</MenuItem>,
								<MenuItem key="previous" value="Previous">{translate("Previous", { defaultValue: "Previous" })}</MenuItem>,
								<MenuItem key="restart" value="Restart">{translate("Restart", { defaultValue: "Restart" })}</MenuItem>,
							]
							: [])}
						
					</Select>
					{selectedActions.value === "open-url" ? (
						<>
							<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px", textAlign: "left" }}>
										{translate("Enter URL")}
							</Typography>
							<TextField
								value={targetURL}
								size="small"
								placeholder="https://quixy.com"
								onChange={(e) => {
									const newURL = e.target.value;
									setTargetURL(newURL);   // Update the `targetURL` state with the new value
									handleURLChange(e);  // Update the selectedButton.targetURL with the new value
									setTargetURLError(validateTargetURL(newURL));
								}
								}
								error={!!targetURLError}
								helperText={targetURLError}
							/>

							<ToggleButtonGroup
								value={selectedTab}
								onChange={handleChangeTabs}
								exclusive
										aria-label={translate("open in tab", { defaultValue: "open in tab" })}
								sx={{
									gap: "5px",
									marginY: "5px",
									height: "35px",
								}}
							>
								{["new-tab", "same-tab"].map((tab) => {
									return (
										<ToggleButton
											value={tab}
											aria-label="new tab"
											sx={{
												border: "1px solid #7EA8A5",
												textTransform: "capitalize",
												color: "#000",
												borderRadius: "4px",
												flex: 1,
												padding: "0 !important",

												"&.Mui-selected": {
													backgroundColor: "var(--border-color)",
													color: "#000",
													border: "2px solid #7EA8A5",
												},
												"&:hover": {
													backgroundColor: "#f5f5f5",
												},
												"&:last-child": {
													borderLeft: "1px solid var(--primarycolor) !important",
												},
											}}
										>
											{tab}
										</ToggleButton>
									);
								})}
							</ToggleButtonGroup>
						</>
						) : null}

					{/* {selectedActions.value === "start-interaction" ? (
						<>
							<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>Choose Interaction</Typography>

							<Autocomplete
								// sx={{ width: 300 }}
								open={openInteractionList}
								value={selectedInteraction}
								onChange={(event, newValue) => {
									setSelectedInteraction(newValue);
								}}
								onOpen={handleOpenInteraction}
								onClose={handleCloseInteraction}
								isOptionEqualToValue={(option, value) => {
									return option.guideId === value.guideId;
								}}
								getOptionLabel={(option) => {
									return option.title;
								}}
								size="small"
								freeSolo
								options={guideListByOrg}
								loading={loading}
								renderInput={(params) => (
									<TextField
										{...params}
										placeholder="Select Interaction"
										variant="outlined"
										slotProps={{
											inputLabel: {
												shrink: false,
											},
										}}
									/>
								)}
							/>
						</>
					) : null} */}
				</FormControl>
					{/* <Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px", textAlign: "left" }}>
						Button Color
					</Typography> */}

					<Box
						className="qadpt-control-box"
						sx={{ borderRadius: "5px" }}
					>
							<div className="qadpt-control-label">{translate("Background", { defaultValue: "Background" })}</div>
							<div>
						<input
							type="color"
							value={tempColors.backgroundColor}
							onChange={(e) => handleColorChange(e, "backgroundColor")}
							className="qadpt-color-input"
								/>
								</div>
					</Box>

					<Box
						className="qadpt-control-box"
						sx={{ borderRadius: "5px" }}
					>
							<div className="qadpt-control-label">{translate("Border", { defaultValue: "Border" })}</div>
							<div>
						<input
							type="color"
							value={tempColors.borderColor}
							onChange={(e) => handleColorChange(e, "borderColor")}
							className="qadpt-color-input"
								/>
								</div>
					</Box>

					<Box
						className="qadpt-control-box"
						sx={{ borderRadius: "5px" }}
					>
							<div className="qadpt-control-label">{translate("Text", { defaultValue: "Text" })}</div>
							<div>
						<input
							type="color"
							value={tempColors.color}
							onChange={(e) => handleColorChange(e, "color")}
							className="qadpt-color-input"
								/>
								</div>
					</Box>
					</div>
					</div>

				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}
						className="qadpt-btn"
					>
						{translate("Apply", { defaultValue: "Apply" })}
					</Button>
				</div>
			</div>
		</div>
		//</Draggable>
	);
};

export default ButtonSettings;
