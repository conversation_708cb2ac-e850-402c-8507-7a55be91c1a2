import React, { useContext, useEffect, useRef, useState } from 'react';
import PopupList from '../guideList/PopupList';
import StopScrapingButton from "../../AI/StopScrapingButton";
import {
    ProductToursicon, Tooltipsicon,
    announcementicon, Bannersicon,
    Checklisticon, Hotspoticon,
    Surveyicon, Announcementsicon,
    ai
} from '../../../assets/icons/icons';
import './GuideMenuOptions.css';
import EnableAIButton from '../../AI/EnableAI';
import useDrawerStore, { DrawerState } from '../../../store/drawerStore';
import { isScrapingActive, stopScraping, startScraping, getScrapedDataCount, loadScrapedDataFromStorage, exportScrapedDataToFile } from '../../../services/ScrapingService';

import { Tooltip } from '@mui/material';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';

import ModernChatWindow from '../../AIAgent/ModernChatWindow';
import CreateWithAIButton from '../../AIAgent/CreateWithAIButton';

import userSession from '../../../store/userSession';
import { AccountContext } from '../../login/AccountContext';
import { IsOpenAIKeyEnabledForAccount } from '../../../services/GuideListServices';

import { useTranslation } from 'react-i18next';



const Guidemenu = ({
	isAIGuidePersisted,
	setIsAIGuidePersisted,
	setStepData,
	onGridItemClick,
	isCollapsed,
	toggleDrawer,
	activeMenu,
	setActiveMenu,
	searchText,
	setSearchText,
	setisShowIcon,
	setIsPopupOpen,
	setIsLoggedIn,
	setIsTourPopupOpen,
	setIsDrawerClosed,
	setShowBannerenduser,
	currentAccountId
}: any) => {
	const { t: translate } = useTranslation();
const{accountId}=useContext(AccountContext);
	const {
		setSelectedTemplate,
		setSelectedTemplateTour,
		setSteps,
		steps,
		setTooltipCount,
		tooltipCount,
		SetGuideName,
		setIsTooltipPopup,
		HotspotGuideDetails,
		setBannerPopup,
		setElementSelected,
		TooltipGuideDetails,
		HotspotGuideDetailsNew,
		setSelectedStepTypeHotspot,
		selectedTemplate,
		selectedTemplateTour,
		activeMenu: drawerActiveMenu,
		searchText: drawerSearchText,
		setActiveMenu: setDrawerActiveMenu,
		setSearchText: setDrawerSearchText,
		isExtensionClosed,
		setIsExtensionClosed,
		isAgentTraining,
		agentName, 
		agentDescription,
		agentUrl
		
	} = useDrawerStore((state: DrawerState) => state);
	useEffect(() => {
		if (isExtensionClosed) {
			setIsExtensionClosed(false);
		}
	}, []);
	// State for AI chat window visibility
	const [isAIChatOpen, setIsAIChatOpen] = useState(false);
	// Get the setCurrentGuideId function from userSession
	const { setCurrentGuideId } = userSession((state: any) => state);


	const { hasAnnouncementOpened, setHasAnnouncementOpened } = userSession();
	const hasTriggered = useRef(false);

	useEffect(() => {
		if (hasAnnouncementOpened && !hasTriggered.current) {
			setDrawerActiveMenu("announcements");
			setDrawerSearchText("Announcement");
			hasTriggered.current = true;
			// optionally reset the flag if you want it to trigger again next login
			setHasAnnouncementOpened(false);
		}
	}, [hasAnnouncementOpened]);

	// Add this effect to handle reopening
	useEffect(() => {
		// When extension is reopened (isExtensionClosed becomes false)
		if (!isExtensionClosed && activeMenu) {
			// Reopen the popup with the previously selected menu
			setDrawerActiveMenu(activeMenu);
			setDrawerSearchText(searchText);
			setIsPopupOpen(true);
		}
	}, [isExtensionClosed, activeMenu, searchText]);
	const menuItems = [
		{
			id: "announcements",
			name: "Announcement", // Use English for logic
			description: translate("Helps to communicate important updates, notifications, or messages.", { defaultValue: "Helps to communicate important updates, notifications, or messages." }),
			icon: <span className={`${isCollapsed ? "qadpt-colsvg" : ""}`}
			dangerouslySetInnerHTML={{ __html: Announcementsicon }} />,
		},
		{
			id: "banners",
			name: "Banner",
			description: translate("Displays notifications at the top or bottom of the screen.", { defaultValue: "Displays notifications at the top or bottom of the screen." }),
			icon: <span dangerouslySetInnerHTML={{ __html: Bannersicon }} />,
		},
		{
			id: "tooltips",
			name: "Tooltip",
			description: translate("Provide quick explanations, tips, or instructions of the tools,", { defaultValue: "Provide quick explanations, tips, or instructions of the tools," }),
			icon: <span dangerouslySetInnerHTML={{ __html: Tooltipsicon }} />,
			disabled: false,
		},
		{
			id: "hotspot",
			name: "Hotspot",
			description: translate("Interactive areas to draw attention to important features, actions, or guidance.", { defaultValue: "Interactive areas to draw attention to important features, actions, or guidance." }),
			icon: (
				<span
				className={`${isCollapsed ? "qadpt-colsvg" : ""}`}
				dangerouslySetInnerHTML={{ __html: Hotspoticon }}
				/>
			),			disabled: false,
		},
		{
			id: "tours",
			name: "Tour",
			description: translate("Step-by-step guides to navigate and understand key features.", { defaultValue: "Step-by-step guides to navigate and understand key features." }),
			icon: <span dangerouslySetInnerHTML={{ __html: ProductToursicon }} />,
			disabled: false,
		},
		{
			id: "checklists",
			name: "Checklist",
			description: translate("Task lists that guide users through a series of steps or actions", { defaultValue: "Task lists that guide users through a series of steps or actions" }),
			icon: (
				<span
					className={`${isCollapsed ? "qadpt-colsvg" : ""}`}
					dangerouslySetInnerHTML={{ __html: Checklisticon }}
				/>
			),			disabled: false,
		},
		{
			id: "survey",
			name: "Survey",
			description: translate("Interactive forms or questionnaires designed to collect feedback, insights, or opinions", { defaultValue: "Interactive forms or questionnaires designed to collect feedback, insights, or opinions" }),
			icon: <span dangerouslySetInnerHTML={{ __html: Surveyicon }} />,
			disabled: true,
		},
	];
	// State for AI scraping
    const [showScrapingButton, setShowScrapingButton] = useState(false);
    const [scrapedCount, setScrapedCount] = useState(0);

    // Check scraping status on component mount and periodically
    useEffect(() => {
        // Initial check
        setShowScrapingButton(isScrapingActive());
        setScrapedCount(getScrapedDataCount());

        // Set up periodic check
        const checkInterval = setInterval(() => {
            setShowScrapingButton(isScrapingActive());
            setScrapedCount(getScrapedDataCount());
        }, 1000);

        // Clean up interval on unmount
        return () => clearInterval(checkInterval);
    }, []);

	const handleEnableAI = async () => {
        // Check if scraping is already active
        if (isScrapingActive()) {
            alert('Scraping is already in progress');
            return;
        }

        try {
            // Start scraping directly without file upload (now async)
            await startScraping();
            setShowScrapingButton(true);
        } catch (error) {
            console.error('Error starting scraping:', error);
            alert('Error starting scraping');
        }
    };

    const handleStopScraping = async () => {
        try {
            // Use the service to stop scraping - don't clear storage for "Stop Training" (save)
            await stopScraping(isAgentTraining, accountId, agentName, agentDescription, agentUrl);
            setShowScrapingButton(false);

            // Show scraped data in console
            if (scrapedCount > 0) {
            } else {
                alert('No elements were scraped.');
            }
        } catch (error) {
            console.error('Error stopping scraping:', error);
            alert('Error stopping scraping');
        }
    };

    const handleViewScrapedData = async () => {
        try {
            

            // Also try to load and display stored data
            const storedData = await loadScrapedDataFromStorage();
            if (storedData) {
                console.group('🗄️ Stored Scraped Data');
                console.log('Stored data:', storedData);
                console.groupEnd();
            }

           // alert(`${scrapedCount} elements have been scraped. Check the console for current and stored data.`);
        } catch (error) {
            console.error('Error viewing scraped data:', error);
           // alert(`${scrapedCount} elements have been scraped. Check the console for details.`);
        }
    };

    const handleExportToFile = async () => {
        try {
            await exportScrapedDataToFile();
        } catch (error) {
            console.error('Error sending to API:', error);
            alert('Error sending data to backend API. Check console for details.');
        }
    };

	const handleMenuClick = async (menuId: string, menuName: string) => {
		if (isCollapsed === true) {
			toggleDrawer(false);
		}
		if (menuItems.find((item) => item.id === menuId)?.disabled) return;

		// Handle AI chat window separately
		if (menuId === "createwithai") {
			setIsAIChatOpen(true);
			return;
		}
		setDrawerActiveMenu(menuId);
		setDrawerSearchText(menuName);
	};

	const handleClosePopup = () => {
		setDrawerActiveMenu(null);
		setDrawerSearchText("");
	};

	const handleCloseAIChat = () => {
		setIsAIChatOpen(false);
	};

	const handleAddClick = (searchText: string, isEditing: boolean = false, guideDetails: any = null) => {
		setisShowIcon(true);
		onGridItemClick(searchText, isEditing, guideDetails);
		setDrawerActiveMenu(menuItems.find((item) => item.name === searchText)?.id || null);
		if (isCollapsed) {
			toggleDrawer();
		}
	};

	return (
		<div style={{ top: isCollapsed ? "20px" : "0px", position: "relative" }}>

			<div className="side-menu priamry-color" style={{ height: "calc(100vh - 65px)", overflow: "hidden" }}>
			<PerfectScrollbar>
					<CreateWithAIButton onClick={() => handleMenuClick("createwithai", translate("Create With AI"))} />
				<ul className="menu-list">
					{menuItems.map((item) => (
						<Tooltip
							key={item.id}
							title={item.disabled ? translate("Coming Soon") : translate(item.name)}
							PopperProps={{ sx: { zIndex: 9999 } }}
						>
							<span style={{ cursor: item.disabled ? "not-allowed" : "pointer" }}>
								<li
									data-id={item.id}
									className={`menu-item ${drawerActiveMenu === item.id ? "active" : ""} ${item.disabled ? "disabled" : ""}`}
									onClick={() => handleMenuClick(item.id, item.name)}
									style={{ opacity: item.disabled ? 0.5 : 1 }}
								>
									<div className="menu-content  priamry-color">
										<span className="icons">
											<div>{item.icon}</div>
										</span>
										{!isCollapsed && (
											<div className="menu-text priamry-color">
												<div className="menu-title">{item.id === "createwithai" ? translate(item.name) : translate(`${item.name}`, { defaultValue: `${item.name}s` })}</div>
												<div className="menu-description">{item.description}</div>
											</div>
										)}
									</div>
								</li>
							</span>
						</Tooltip>
					))}
				</ul>
				{/* Render popups for each menu item */}
				{!isCollapsed && !isAIChatOpen && menuItems.map((item) => (
					<PopupList
						key={item.id}
						title={item.name}
						Open={drawerActiveMenu === item.id}
						onClose={handleClosePopup}
						searchText={drawerSearchText}
						onAddClick={handleAddClick}
						isAIGuidePersisted={isAIGuidePersisted}
						setIsAIGuidePersisted={setIsAIGuidePersisted}
					/>
				))}
			</PerfectScrollbar>
		</div>
		{/* AI Chat Window */}
		{isAIChatOpen && (
			<ModernChatWindow
				onClose={handleCloseAIChat}
				setStepData={setStepData}
				setIsAIChatOpen={setIsAIChatOpen}
				setCurrentGuideId={setCurrentGuideId}
				setIsPopupOpen={setIsPopupOpen}
				setIsLoggedIn={setIsLoggedIn}
				setIsTourPopupOpen={setIsTourPopupOpen}
				setIsDrawerClosed={setIsDrawerClosed}
				setShowBannerenduser={setShowBannerenduser}
			/>
		)}
		{/* <EnableAIButton onClick={handleEnableAI} /> */}
		{showScrapingButton && (
			<div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
				<StopScrapingButton onClick={handleStopScraping} />
				{scrapedCount > 0 && (
					<>
						<div style={{
							fontSize: '12px',
							color: '#4CAF50',
							textAlign: 'center',
							cursor: 'pointer',
							padding: '4px 8px',
							backgroundColor: 'rgba(76, 175, 80, 0.1)',
							borderRadius: '4px',
							border: '1px solid rgba(76, 175, 80, 0.3)'
						}} onClick={handleViewScrapedData}>
							📊 {scrapedCount} elements scraped (click to view)
						</div>
						<button style={{
							fontSize: '12px',
							color: '#2196F3',
							backgroundColor: 'rgba(33, 150, 243, 0.1)',
							border: '1px solid rgba(33, 150, 243, 0.3)',
							borderRadius: '4px',
							padding: '6px 12px',
							cursor: 'pointer',
							fontWeight: 'bold'
						}} onClick={handleExportToFile}>
							🚀 Send to API
						</button>
					</>
				)}
			</div>
		)}
	</div>
);
}

export default Guidemenu;
