import {
	Autocomplete,
	Box,
	Button,
	FormControl,
	IconButton,
	MenuItem,
	Popover,
	Select,
	SelectChangeEvent,
	TextField,
	ToggleButton,
	ToggleButtonGroup,
	Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useEffect, useState } from "react";
import useDrawerStore, { TInteractionValue } from "../../../store/drawerStore";
import React from "react";
import { ChromePicker, ColorResult } from "react-color";
import { GetGudeDetailsByGuideId } from "../../../services/GuideListServices";
import userSession from "../../../store/userSession";
import { useTranslation } from 'react-i18next';

const ButtonSetting = ({
	settingAnchorEl,
	handleCloseSettingPopup,
	guideListByOrg,
	loading,
	handleApplyChanges,
	buttonInfo,
	updatedGuideData,
}: any) => {
	const { t: translate } = useTranslation();
	const [currentButtonName, setCurrentButtonName] = useState("");
	const [currentSelectedColor, setCurrentSelectedColor] = useState({
		color: "",
		target: "",
	});
	const {
		ButtonsDropdown,
		setButtonsDropdown,
		toolTipGuideMetaData,
		updateTooltipButtonAction,
		btnidss,
		setBtnIdss,
		currentStep,
		setCurrentStep,
		highlightedButton,
		getCurrentButtonInfo,
		interactionData,
		createWithAI,
		selectedTheme,
		applyThemeButtonInTooltip,
		selectedTemplate,
	} = useDrawerStore((state: any) => state);
	const [selectedTab, setSelectedTab] = useState("new-tab");
	const [selectedInteraction, setSelectedInteraction] = useState<string | null>("");
	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLDivElement | null>(null);
	const [selectedActions, setSelectActions] = useState<TInteractionValue>("close");
	const [targetURL, setTargetURL] = useState("");
	const [targetURLError, setTargetURLError] = useState("");
	const { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);
	const [tempColors, setTempColors] = useState({
		backgroundColor: "#5F9EA0",
		borderColor: "#70afaf",
		color: "#ffffff",
	});

	useEffect(() => {
		// Only fetch button info when we have valid container and button IDs
		if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {
			const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);
			// Set all button properties from the result
			if (result) {
				// Set button name
				if (result.title) {
					setCurrentButtonName(result.title);
				}
				// Set button action
				if (result.selectedActions) {
					setSelectActions(result.selectedActions as TInteractionValue);
				}
				// Set target URL
				if (result.targetURL) {
					setTargetURL(result.targetURL);
				}
				// Set colors
				setTempColors({
					backgroundColor: result.bgColor || "#5F9EA0",
					borderColor: result.borderColor || "#70afaf",
					color: result.textColor || "#ffffff",
				});
			}
		}
	}, [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo]);

	useEffect(() => {
		const fetchGuideDetails = async () => {
			if (currentGuideId && currentGuideId !== "") {
				const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);
				const guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];
				const gotoNextButtonId = guideStep?.Design?.GotoNext?.ButtonId;

				if (btnidss === buttonInfo.id) {
					const matchingButton = guideStep?.ButtonSection?.[0]?.CustomButtons?.find(
						(btn: any) => btn.ButtonId === buttonInfo.id
					);

					setSelectActions(btnidss === buttonInfo.id ? "Next" : "close");
				} else if (buttonInfo) {
					setSelectActions(buttonInfo.actions?.value || "close");
				}
			}
		};

		fetchGuideDetails();

		if (settingAnchorEl.value && buttonInfo) {
			setCurrentButtonName(buttonInfo.name);
			setSelectedInteraction(buttonInfo.actions?.interaction || "");
			setSelectActions(buttonInfo.actions?.value || "");
			setTargetURL(buttonInfo.actions?.targetURL || buttonInfo.actions?.targetUrl || "");
			setSelectedTab(buttonInfo.actions?.tab || "");
			setTempColors({
				backgroundColor: buttonInfo.style.backgroundColor,
				borderColor: buttonInfo.style.borderColor,
				color: buttonInfo.style.color,
			});
		}
	}, [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep]);

	const handleChangeActions = (e: SelectChangeEvent) => {
		const v = e.target.value;
		setSelectActions(v as TInteractionValue);
		if (ButtonsDropdown === currentButtonName) {
			const v = e.target.value;
			setSelectActions(v as TInteractionValue);
		}
	};

	useEffect(() => {}, [ButtonsDropdown]);

	const handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {
		setSelectedTab((event.target as HTMLInputElement).value);
	};
	const handleColorChange = (e: any, targetName: any) => {
		const value = e.hex;
		setTempColors((prev) => ({
			...prev,
			[currentSelectedColor.target]: currentSelectedColor.target === "borderColor" ? `2px solid ${value}` : value,
		}));
		setCurrentSelectedColor((prevState) => {
			return {
				...prevState,
				color: value,
			};
		});
	};
	const validateTargetURL = (url: string) => {
		if (selectedActions === "open-url") {
			if (!url) {
				return "URL is required";
			}
			try {
				new URL(url);
				return "";
			} catch (error) {
				return "Invalid URL";
			}
		}
		return "";
	};

	const handleChanges = () => {
		const targetURLError = validateTargetURL(targetURL);
		setTargetURLError(targetURLError);

		if (targetURLError) {
			return;
		}

		// Retain the previously saved button name if the field is empty
		let buttonNameToUpdate = currentButtonName;
		// If the current button name is empty, try to get it from buttonInfo or getCurrentButtonInfo
		if (!buttonNameToUpdate || !buttonNameToUpdate.trim()) {
			if (buttonInfo && buttonInfo.name) {
				buttonNameToUpdate = buttonInfo.name;
			} else if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {
				const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);
				if (result?.title) {
					buttonNameToUpdate = result.title;
				}
			}
		}

		// Apply the changes with the updated button name

		handleApplyChanges(tempColors, selectedActions, targetURL, selectedInteraction, buttonNameToUpdate, selectedTab);
	};
		const [buttonShape, setButtonShape] = useState<"square" | "curved" | "round">("square");
	
	useEffect(() => {
		  
			const buttonStyles = selectedTheme?.ThemeStyles?.Button;
			if (buttonStyles) {
			  setTempColors({
				backgroundColor: buttonStyles.primaryBtnBg ,
				borderColor: buttonStyles.secondaryBtnBg,
				color: buttonStyles.primaryBtnColor,
			  });
			  setButtonShape(buttonStyles.shape || "square");
			  if (!selectedTheme?.ThemeStyles?.Button) return;
	
			  applyThemeButtonInTooltip(selectedTheme.ThemeStyles.Button);
			}
		  }, [ selectedTheme]);
	const handleURLChange = (e: any) => {
		const newURL = e.target.value;
		setTargetURL(newURL);
	};

	return (
		<Popover
			id={"btn-setting-toolbar"}
			open={Boolean(settingAnchorEl.value)}
			anchorEl={settingAnchorEl.value}
			onClose={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}
			// slotProps={{
			// 	root: {
			// 		// instead of writing sx on popover write here it also target to root and more clear
			// 		sx: {
			// 			zIndex: (theme) => theme.zIndex.tooltip + 1000,
			// 		},
			// 	},
			// }}
			slotProps={{
				paper: {
				  sx: {
						boxShadow: "none !important",
						backgroundColor: "transparent"
				  },
				},
			  }}	  
		>
			<div
				id="qadpt-designpopup"
				className="qadpt-designpopup qadpt-tltbtnprop"
			>
				<div className="qadpt-content">
					<div className="qadpt-design-header">
						<div className="qadpt-title">{translate("Properties")}</div>
						<IconButton
							size="small"
							aria-label={translate("Close")}
							onClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}
						>
							<CloseIcon />
						</IconButton>
					</div>
					<div className="qadpt-controls">
						<FormControl
							fullWidth
							sx={{ marginBottom: "16px" }}
						>
							<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>{translate("Button Name")}</Typography>
							<TextField
								value={currentButtonName}
								size="small"
								sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
								placeholder={translate("Button Name")}
								onChange={(e) => setCurrentButtonName(e.target.value)}
							/>
							<Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px" }}>{translate("Button Action")}</Typography>
							<Select
								value={selectedActions}
								onChange={handleChangeActions}
										sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
									textAlign: "left",
									"& .MuiSelect-select": {
								padding: "8px",
							},
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
								MenuProps={{
									slotProps: {
										// disablePortal: true,
										root: {
											disablePortal: true,
											disableEnforceFocus: true, // Prevents focus trapping
											disableAutoFocus: true, // Allows the input field to gain focus
											disableRestoreFocus: true,

											sx: {
												zIndex: (theme) => theme.zIndex.tooltip + 1200,
											},
										},
									},
								}}
							>
								<MenuItem value="close">{translate("Close")}</MenuItem>
								<MenuItem value="open-url">{translate("Open URL")}</MenuItem>
								{(selectedTemplate === "Tour" ||
									(selectedTemplate !== "Banner" && selectedTemplate !== "Hotspot")
									? [
										<MenuItem key="next" value="Next">{translate("Next")}</MenuItem>,
										<MenuItem key="previous" value="Previous">{translate("Previous")}</MenuItem>,
										<MenuItem key="restart" value="Restart">{translate("Restart")}</MenuItem>,
									]
									: [])}
							</Select>
							{selectedActions === "open-url" ? (
								<>
									<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>{translate("Enter URL")}</Typography>
									<TextField
										value={targetURL}
										size="small"
										placeholder="https://quixy.com"
										onChange={(e) => {
											const newURL = e.target.value;
											setTargetURL(newURL); // Update the `targetURL` state with the new value
											handleURLChange(e); // Update the selectedButton.targetURL with the new value
										}}
										error={!!targetURLError}
										helperText={targetURLError ? translate(targetURLError) : ""}
									/>

									<ToggleButtonGroup
										value={selectedTab}
										onChange={handleChangeTabs}
										exclusive
										aria-label={translate("open in tab")}
										sx={{
											gap: "5px",
											marginY: "5px",
											height: "35px",
										}}
									>
										{["new-tab", "same-tab"].map((tab) => {
											return (
												<ToggleButton
													value={tab}
													aria-label="new tab"
													sx={{
														border: "1px solid #7EA8A5",
														textTransform: "capitalize",
														color: "#000",
														borderRadius: "4px",
														flex: 1,
														padding: "0 !important",

														"&.Mui-selected": {
															backgroundColor: "var(--border-color)",
															color: "#000",
															border: "2px solid #7EA8A5",
														},
														"&:hover": {
															backgroundColor: "#f5f5f5",
														},
														"&:last-child": {
															borderLeft: "1px solid var(--primarycolor) !important", // Remove left border for the last button
														},
													}}
												>
													{tab}
												</ToggleButton>
											);
										})}
									</ToggleButtonGroup>
								</>
							) : null}
						</FormControl>

						{/* <Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px" }}>Button Color</Typography> */}

						<Box
							className="qadpt-control-box"
							sx={{ borderRadius: "5px" }}
						>
							<div className="qadpt-control-label">{translate("Background")}</div>
							<Box
								sx={{
									backgroundColor: tempColors.backgroundColor,
									width: "20px",
									height: "20px",
									borderRadius: "50%",
								}}
								component={"div"}
								role="button"
								onClick={(e) => {
									setColorPickerAnchorEl(e.currentTarget);
									setCurrentSelectedColor({
										color: tempColors.backgroundColor,
										target: "backgroundColor",
									});
								}}
							/>
						</Box>

						<Box
							className="qadpt-control-box"
							sx={{ borderRadius: "5px" }}
						>
							<div className="qadpt-control-label">{translate("Border")}</div>
							<Box
								sx={{
									backgroundColor: tempColors.borderColor.split(" ")[2],
									width: "20px",
									height: "20px",
									borderRadius: "50%",
								}}
								component={"div"}
								role="button"
								onClick={(e) => {
									setColorPickerAnchorEl(e.currentTarget);
									setCurrentSelectedColor({
										color: `2px solid ${tempColors.borderColor.split(" ")[2]}`,
										target: "borderColor",
									});
								}}
							/>
						</Box>

						<Box
							className="qadpt-control-box"
							sx={{ borderRadius: "5px" }}
						>
							<div className="qadpt-control-label">{translate("Text")}</div>
							<Box
								sx={{
									backgroundColor: tempColors.color,
									width: "20px",
									height: "20px",
									borderRadius: "50%",
								}}
								component={"div"}
								role="button"
								onClick={(e) => {
									setColorPickerAnchorEl(e.currentTarget);
									setCurrentSelectedColor({
										color: tempColors.color,
										target: "color",
									});
								}}
							/>
						</Box>
					</div>

					<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleChanges}
							className="qadpt-btn"
						>
							{translate("Apply")}
						</Button>
					</div>
				</div>
			</div>
			<Popover
				open={Boolean(colorPickerAnchorEl)}
				anchorEl={colorPickerAnchorEl}
				onClose={() => {
					setColorPickerAnchorEl(null);
					setCurrentSelectedColor({ color: "", target: "" });
				}}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
				id="color-picker"
				slotProps={{
					root: {
						// instead of writing sx on popover write here it also target to root and more clear
						sx: {
							zIndex: (theme) => theme.zIndex.tooltip + 1000,
						},
					},
				}}
			>
				<Box>
					<ChromePicker
						color={currentSelectedColor.color}
						onChange={(e) => handleColorChange(e, currentSelectedColor.target)}
					/>
					<style>
						{`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
					</style>
				</Box>
			</Popover>
		</Popover>
	);
};

export default ButtonSetting;
